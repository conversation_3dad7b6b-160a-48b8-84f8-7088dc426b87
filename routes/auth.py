# routes/auth.py

from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_user, logout_user, login_required, current_user
import logging
from models.user import User
from data.data import load_profiles, save_profiles

auth = Blueprint('auth', __name__, url_prefix='/auth')

@auth.route('/login', methods=['GET', 'POST'])

# --- Function: login ---
def login():
    """Handle user login."""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        if not username or not password:
            flash('Please enter both username and password.', 'danger')
            return render_template('login.html')
        profiles = load_profiles()
        if username in profiles:
            user_profile = profiles[username]
            stored_password_hash = user_profile.get('password_hash')
            logging.debug(f"User {username} found. Stored hash: {stored_password_hash}")
            if stored_password_hash:
                user = User(username, is_admin=user_profile.get('is_admin', False))
                user.password_hash = stored_password_hash
                if user.verify_password(password):
                    login_user(user)
                    flash('Logged in successfully.', 'success')
                    logging.info(f"User '{username}' logged in.")
                    return redirect(url_for('main.index'))
                else:
                    logging.warning(f"Password verification failed for user '{username}'.")
        else:
            logging.warning(f"User '{username}' not found in profiles.")
        flash('Invalid username or password.', 'danger')
    return render_template('login.html')


@auth.route('/logout')
@login_required

# --- Function: logout ---
def logout():
    """Handle user logout."""
    username = current_user.get_id()
    logout_user()
    flash('You have been logged out.', 'info')
    logging.info(f"User '{username}' logged out.")
    return redirect(url_for('auth.login'))

@auth.route('/register', methods=['GET', 'POST'])

# --- Function: register ---
def register():
    """Handle user registration with required fields only."""
    if request.method == 'POST':
        # Get required fields
        username = request.form.get('username')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        name = request.form.get('name')
        email = request.form.get('email')

        # Validate required fields
        if not all([username, password, confirm_password, name, email]):
            flash('Please fill out all required fields.', 'danger')
            return render_template('register.html')

        if password != confirm_password:
            flash('Passwords do not match.', 'danger')
            return render_template('register.html')

        # Check if username already exists
        profiles = load_profiles()
        if username in profiles:
            flash('Username already exists.', 'danger')
            return render_template('register.html')

        # Create new user with required fields only
        password_hash = User.generate_password_hash_static(password)
        new_user = {
            "user_id": len(profiles) + 1,
            "username": username,
            "password_hash": password_hash,
            "name": name,
            "email": email,
            "phone_number": "",
            "age": 0,
            "gender": "",
            "academic_level": "",
            "preferred_subject": "",
            "learning_style": "default",
            "location": "",
            "language": "",
            "timezone": "",
            "cultural_background": "",
            "hobbies": [],
            "goals": [],
            "strengths": [],
            "weaknesses": [],
            "is_admin": False,
            "histories": [],
            "profile_completed": False  # Track profile completion status
        }

        profiles[username] = new_user
        save_profiles(profiles)

        # Log the user in automatically after registration
        user = User(username, is_admin=False)
        user.password_hash = password_hash
        user.name = name
        login_user(user)

        flash('Registration successful! Please complete your profile to personalize your experience.', 'success')
        logging.info(f"New user registered and logged in: '{username}'.")
        return redirect(url_for('auth.complete_profile'))

    return render_template('register.html')


@auth.route('/complete_profile', methods=['GET', 'POST'])
@login_required

# --- Function: complete_profile ---
def complete_profile():
    """Handle profile completion after registration."""
    if request.method == 'POST':
        # Get all optional profile fields from the form
        profile_updates = {
            'age': int(request.form.get('age', 0)) if request.form.get('age') else 0,
            'gender': request.form.get('gender', ''),
            'phone_number': request.form.get('phone_number', ''),
            'academic_level': request.form.get('academic_level', ''),
            'preferred_subject': request.form.get('preferred_subject', ''),
            'learning_style': request.form.get('learning_style', 'default'),
            'location': request.form.get('location', ''),
            'language': request.form.get('language', ''),
            'timezone': request.form.get('timezone', ''),
            'cultural_background': request.form.get('cultural_background', ''),
            'profile_completed': True
        }

        # Process list fields (hobbies, goals, strengths, weaknesses)
        hobbies_str = request.form.get('hobbies', '')
        goals_str = request.form.get('goals', '')
        strengths_str = request.form.get('strengths', '')
        weaknesses_str = request.form.get('weaknesses', '')

        # Convert comma-separated strings to lists
        profile_updates['hobbies'] = [h.strip() for h in hobbies_str.split(',') if h.strip()] if hobbies_str else []
        profile_updates['goals'] = [g.strip() for g in goals_str.split(',') if g.strip()] if goals_str else []
        profile_updates['strengths'] = [s.strip() for s in strengths_str.split(',') if s.strip()] if strengths_str else []
        profile_updates['weaknesses'] = [w.strip() for w in weaknesses_str.split(',') if w.strip()] if weaknesses_str else []

        # Initialize gamification fields if not present
        profile_updates.update({
            'xp': 0,
            'level': 1,
            'streak': 0,
            'badges': [],
            'answer_type_weights': {
                'informational': 0.5,
                'real_world': 0.5,
                'cause_and_effect': 0.5,
                'goal_based': 0.5
            },
            'weight_adjustment_count': 0
        })

        # Update the user's profile
        from data.data import update_profile
        update_profile(current_user.get_id(), profile_updates)

        flash('Profile completed successfully! Welcome to your personalized learning experience.', 'success')
        logging.info(f"Profile completed for user: '{current_user.get_id()}'.")
        return redirect(url_for('main.index'))

    # GET request - show the profile completion form
    return render_template('complete_profile.html', username=current_user.username)
