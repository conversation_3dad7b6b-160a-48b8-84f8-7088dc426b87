# generate_profiles.py

from werkzeug.security import generate_password_hash
import json


# --- Function: generate_profiles ---
def generate_profiles():
    users = [
        {
            "username": "user1",
            "password": "password1",
            "name": "<PERSON>",
            "email": "<EMAIL>",
            "phone_number": "************",
            "age": 57,
            "gender": "female",
            "academic_level": "Kindergarten",
            "preferred_subject": "Math",
            "learning_style": "Visual",
            "location": "USA",
            "language": "English",
            "timezone": "America/New_York",
            "cultural_background": "American",
            "hobbies": ["Drawing", "Playing with blocks"],
            "goals": ["Learn to read", "Make friends"],
            "strengths": ["Creativity", "Imagination"],
            "weaknesses": ["Short attention span"],
            "is_admin": False
        },
        # ... add additional users ...
    ]

    profiles = []
    for i, user in enumerate(users, start=1):
        profile = {
            "user_id": i,
            "username": user["username"],
            "password_hash": generate_password_hash(user["password"]),  # No method specified
            "name": user["name"],
            "email": user["email"],
            "phone_number": user["phone_number"],
            "age": user["age"],
            "gender": user["gender"],
            "academic_level": user["academic_level"],
            "preferred_subject": user["preferred_subject"],
            "learning_style": user["learning_style"],
            "location": user["location"],
            "language": user["language"],
            "timezone": user["timezone"],
            "cultural_background": user["cultural_background"],
            "hobbies": user["hobbies"],
            "goals": user["goals"],
            "strengths": user["strengths"],
            "weaknesses": user["weaknesses"],
            "is_admin": user["is_admin"],
            "histories": []
        }
        profiles.append(profile)

    with open('profiles.json', 'w') as f:
        json.dump(profiles, f, indent=4)

    print("profiles.json has been created with hashed passwords.")

if __name__ == '__main__':
    generate_profiles()
