<!-- templates/complete_profile.html -->

{% extends "base.html" %}

{% block title %}Complete Your Profile - Adaptive Virtual Assistant{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card p-4 shadow-lg">
                <div class="card-header bg-gradient-success text-black text-center">
                    <h2 class="mb-0">Complete Your Profile</h2>
                    <p class="mb-0 mt-2">Help us personalize your learning experience</p>
                </div>
                <div class="card-body">
                    <div class="alert alert-success mb-4">
                        <i class="bi bi-check-circle me-2"></i>
                        <strong>Welcome {{ username }}!</strong> Your account has been created successfully. 
                        Please complete your profile below to get the most personalized learning experience.
                    </div>

                    <form method="POST" action="{{ url_for('auth.complete_profile') }}" aria-label="Profile Completion Form">

                        <!-- Personal Information -->
                        <h5 class="mb-3 text-primary"><i class="bi bi-person-circle me-2"></i>Personal Information</h5>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="age" class="form-label"><i class="bi bi-calendar-check-fill me-2"></i>Age:</label>
                                <input type="number" id="age" name="age" class="form-control" min="3" max="120"
                                       placeholder="Enter your age" title="Must be between 3 and 120.">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="gender" class="form-label"><i class="bi bi-person-badge-fill me-2"></i>Gender:</label>
                                <input type="text" id="gender" name="gender" class="form-control" maxlength="20" 
                                       placeholder="Enter your gender (optional)">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="phone_number" class="form-label"><i class="bi bi-phone-fill me-2"></i>Phone Number:</label>
                            <input type="tel" id="phone_number" name="phone_number" class="form-control"
                                   pattern="^\d{3}-\d{3}-\d{4}$" placeholder="************"
                                   title="Use format: ************">
                        </div>

                        <!-- Academic Information -->
                        <h5 class="mb-3 text-primary mt-4"><i class="bi bi-book-fill me-2"></i>Academic Information</h5>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="academic_level" class="form-label">Academic Level:</label>
                                <input type="text" id="academic_level" name="academic_level" class="form-control" maxlength="50"
                                       placeholder="e.g. Kindergarten, Elementary, High School">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="preferred_subject" class="form-label">Preferred Subject:</label>
                                <input type="text" id="preferred_subject" name="preferred_subject" class="form-control" maxlength="50"
                                       placeholder="e.g. Math, Science, History">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="learning_style" class="form-label"><i class="bi bi-lightbulb-fill me-2"></i>Learning Style:</label>
                            <select id="learning_style" name="learning_style" class="form-select">
                                <option value="">-- Select your preferred learning style --</option>
                                <option value="Visual">Visual (learn through seeing)</option>
                                <option value="Auditory">Auditory (learn through hearing)</option>
                                <option value="Kinesthetic">Kinesthetic (learn through doing)</option>
                                <option value="Reading/Writing">Reading/Writing (learn through text)</option>
                            </select>
                        </div>

                        <!-- Location & Language -->
                        <h5 class="mb-3 text-primary mt-4"><i class="bi bi-globe me-2"></i>Location & Language</h5>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="location" class="form-label"><i class="bi bi-geo-alt-fill me-2"></i>Location:</label>
                                <input type="text" id="location" name="location" class="form-control" maxlength="50" 
                                       placeholder="Enter your country/region">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="language" class="form-label"><i class="bi bi-translate me-2"></i>Language:</label>
                                <input type="text" id="language" name="language" class="form-control" maxlength="30" 
                                       placeholder="Preferred language">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="timezone" class="form-label"><i class="bi bi-clock-fill me-2"></i>Timezone:</label>
                                <input type="text" id="timezone" name="timezone" class="form-control" maxlength="50" 
                                       placeholder="e.g. America/New_York">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="cultural_background" class="form-label">Cultural Background:</label>
                                <input type="text" id="cultural_background" name="cultural_background" class="form-control" maxlength="50"
                                       placeholder="e.g. American, Chinese, Mixed">
                            </div>
                        </div>

                        <!-- Interests & Goals -->
                        <h5 class="mb-3 text-primary mt-4"><i class="bi bi-star-fill me-2"></i>Interests & Goals</h5>
                        
                        <div class="mb-3">
                            <label for="hobbies" class="form-label"><i class="bi bi-palette-fill me-2"></i>Hobbies:</label>
                            <input type="text" id="hobbies" name="hobbies" class="form-control" maxlength="100" 
                                   placeholder="e.g. Drawing, Playing with blocks, Reading">
                            <div class="form-text">Separate multiple hobbies with commas</div>
                        </div>

                        <div class="mb-3">
                            <label for="goals" class="form-label"><i class="bi bi-bullseye me-2"></i>Learning Goals:</label>
                            <input type="text" id="goals" name="goals" class="form-control" maxlength="100" 
                                   placeholder="e.g. Learn to read, Improve math skills, Make friends">
                            <div class="form-text">What would you like to achieve?</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="strengths" class="form-label"><i class="bi bi-star-fill me-2"></i>Strengths:</label>
                                <input type="text" id="strengths" name="strengths" class="form-control" maxlength="100" 
                                       placeholder="e.g. Creativity, Imagination, Problem-solving">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="weaknesses" class="form-label"><i class="bi bi-exclamation-circle-fill me-2"></i>Areas to Improve:</label>
                                <input type="text" id="weaknesses" name="weaknesses" class="form-control" maxlength="100" 
                                       placeholder="e.g. Short attention span, Math anxiety">
                            </div>
                        </div>

                        <div class="alert alert-info mt-4">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Note:</strong> All fields are optional. You can skip any information you don't want to share 
                            and update your profile later from your account settings.
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <button type="button" class="btn btn-outline-secondary me-md-2" onclick="skipProfile()">
                                Skip for Now
                            </button>
                            <button type="submit" class="btn btn-primary">
                                Complete Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function skipProfile() {
    if (confirm('Are you sure you want to skip profile completion? You can always complete it later from your profile settings.')) {
        window.location.href = "{{ url_for('main.index') }}";
    }
}

document.addEventListener('DOMContentLoaded', () => {
    // Add some interactive feedback
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, select');
    
    inputs.forEach(input => {
        input.addEventListener('input', () => {
            if (input.value.trim()) {
                input.classList.add('is-valid');
                input.classList.remove('is-invalid');
            } else {
                input.classList.remove('is-valid', 'is-invalid');
            }
        });
    });
});
</script>

{% endblock %}
