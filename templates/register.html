<!-- templates/register.html -->

{% extends "base.html" %}

{% block title %}Register - Adaptive Virtual Assistant{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card p-4 shadow-lg">
                <div class="card-header bg-gradient-primary text-black text-center">
                    <h2 class="mb-0">Create an Account</h2>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('auth.register') }}" aria-label="Registration Form">

                        <!-- Required Fields -->
                        <div class="mb-3">
                            <label for="name" class="form-label"><i class="bi bi-person-circle me-2"></i>Full Name:</label>
                            <input type="text" id="name" name="name" class="form-control" required pattern="^[A-Za-z\s]{2,50}$"
                                   title="Only letters and spaces allowed. 2–50 characters." placeholder="Enter your full name">
                        </div>

                        <div class="mb-3">
                            <label for="username" class="form-label"><i class="bi bi-person-fill me-2"></i>Username:</label>
                            <input type="text" id="username" name="username" class="form-control" required pattern="^[a-zA-Z0-9_]{4,25}$"
                                   title="Use 4–25 characters: letters, numbers, or underscores." placeholder="Choose a username">
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label"><i class="bi bi-envelope-fill me-2"></i>Email:</label>
                            <input type="email" id="email" name="email" class="form-control" required placeholder="Enter your email">
                        </div>

                        <div class="alert alert-info mb-3">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Next Step:</strong> After creating your account, you'll be able to complete your profile with additional information to personalize your learning experience.
                        </div>

                        <!-- Password Fields -->
                        <div class="mb-3">
                            <label for="password" class="form-label"><i class="bi bi-lock-fill me-2"></i>Password:</label>
                            <input type="password" id="password" name="password" class="form-control" required minlength="6" maxlength="32"
                                   placeholder="Enter your password">
                        </div>

                        <div class="mb-4">
                            <label for="confirm_password" class="form-label"><i class="bi bi-lock-fill me-2"></i>Confirm Password:</label>
                            <input type="password" id="confirm_password" name="confirm_password" class="form-control" required minlength="6" maxlength="32"
                                   placeholder="Re-enter your password">
                        </div>

                        <button type="submit" class="btn btn-primary w-100">Register</button>
                    </form>

                    <p class="mt-3 text-center">
                        Already have an account? <a href="{{ url_for('auth.login') }}" class="text-primary">Login here</a>.
                    </p>

                    <script>
                        document.addEventListener('DOMContentLoaded', () => {
                            const form = document.querySelector('form');
                            const password = document.getElementById('password');
                            const confirmPassword = document.getElementById('confirm_password');
                    
                            form.addEventListener('submit', (e) => {
                                // Password match check
                                if (password.value !== confirmPassword.value) {
                                    e.preventDefault();
                                    confirmPassword.setCustomValidity("Passwords do not match.");
                                    confirmPassword.reportValidity();
                                } else {
                                    confirmPassword.setCustomValidity("");
                                }
                    
                    
                            });
                        });
                    </script>
                    
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
